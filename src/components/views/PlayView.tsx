import { useState, useEffect, useRef } from "react";
import { useEnygmaGame } from "../../contexts/EnygmaGameContext";
import "./PlayView.scss";

interface PlayViewProps {
  onBackToMain: () => void;
}

interface ChatMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
}

const PlayView: React.FC<PlayViewProps> = ({ onBackToMain }) => {
  const { session, askQuestion } = useEnygmaGame();
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new messages arrive
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Sync messages from game session
  useEffect(() => {
    if (session?.messages) {
      const chatMessages: ChatMessage[] = session.messages.map((msg) => ({
        id: msg.id,
        text: msg.text,
        sender: msg.sender,
        timestamp: msg.timestamp,
      }));
      setMessages(chatMessages);
    }
  }, [session?.messages]);

  // Add initial welcome message if no session messages yet
  useEffect(() => {
    if (session && (!session.messages || session.messages.length === 0) && messages.length === 0) {
      const welcomeMessage: ChatMessage = {
        id: "welcome",
        text: session.mode === "yo_pienso"
          ? "¡Hola! Soy Enygma. Piensa en un personaje del entretenimiento y yo intentaré adivinarlo haciendo preguntas. Responde solo con 'Sí', 'No', 'Tal vez' o 'No lo sé'. ¿Estás listo?"
          : "¡Hola! Soy Enygma. He pensado en un personaje del entretenimiento. Haz preguntas que pueda responder con 'Sí', 'No', 'Tal vez' o 'No lo sé'. ¿Empezamos?",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages([welcomeMessage]);
    }
  }, [session, messages.length]);

  const handleSendMessage = async () => {
    if (!inputText.trim() || isLoading || !session) return;

    const messageText = inputText.trim();
    setInputText("");
    setIsLoading(true);

    try {
      // Send question to AI through game context
      await askQuestion(messageText);
    } catch (error) {
      console.error("Error sending message:", error);
      // Add error message
      const errorMessage: ChatMessage = {
        id: `error-${Date.now()}`,
        text: "Lo siento, hubo un error al procesar tu mensaje. Inténtalo de nuevo.",
        sender: "ai",
        timestamp: new Date(),
      };
      setMessages((prev) => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // Show loading or error state if no session
  if (!session) {
    return (
      <div className="content">
        <div className="play-view">
          <div className="play-header">
            <button onClick={onBackToMain} className="back-button">
              ← Volver al menú
            </button>
            <h2>Iniciando juego...</h2>
          </div>
          <div className="chat-container">
            <div className="welcome-message">
              <p>Preparando el juego con Enygma...</p>
              <p>Por favor, espera un momento.</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="content">
      <div className="play-view">
        {/* Header */}
        <div className="play-header">
          <button onClick={onBackToMain} className="back-button">
            ← Volver al menú
          </button>
          <h2>Conversación con Enygma</h2>
          {session && (
            <div className="game-info">
              <span>Modo: {session.mode === "yo_pienso" ? "Yo pienso" : "Aura piensa"}</span>
              <span>Preguntas: {session.questionCount}/{session.maxQuestions}</span>
            </div>
          )}
        </div>

        {/* Chat Messages */}
        <div className="chat-container">
          <div className="messages-list">
            {messages.length === 0 && (
              <div className="welcome-message">
                <p>¡Hola! Soy Enygma. Estoy listo para jugar contigo.</p>
                <p>
                  {session?.mode === "yo_pienso"
                    ? "Piensa en un personaje y yo intentaré adivinarlo haciendo preguntas."
                    : "Yo pensaré en un personaje y tú tendrás que adivinarlo."}
                </p>
              </div>
            )}

            {messages.map((message) => (
              <div
                key={message.id}
                className={`message ${message.sender === "user" ? "user-message" : "ai-message"}`}
              >
                <div className="message-content">
                  <span className="message-text">{message.text}</span>
                  <span className="message-time">
                    {message.timestamp.toLocaleTimeString([], {
                      hour: "2-digit",
                      minute: "2-digit",
                    })}
                  </span>
                </div>
              </div>
            ))}

            {isLoading && (
              <div className="message ai-message">
                <div className="message-content">
                  <span className="message-text typing">Enygma está pensando...</span>
                </div>
              </div>
            )}

            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Input Area */}
        <div className="chat-input-container">
          <div className="input-wrapper">
            <input
              type="text"
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={
                session?.mode === "yo_pienso"
                  ? "Responde con 'Sí', 'No', 'Tal vez' o 'No lo sé'..."
                  : "Haz una pregunta sobre el personaje..."
              }
              disabled={isLoading || !session}
              className="chat-input"
            />
            <button
              onClick={handleSendMessage}
              disabled={!inputText.trim() || isLoading || !session}
              className="send-button"
            >
              {isLoading ? "Enviando..." : "Enviar"}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlayView;
