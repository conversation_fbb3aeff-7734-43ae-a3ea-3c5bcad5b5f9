.play-view {
  display: flex;
  flex-direction: column;
  height: 100vh;
  max-height: 100vh;
  overflow: hidden;
}

.play-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 2rem;
  background: rgba(0, 20, 40, 0.9);
  border-bottom: 1px solid rgba(64, 224, 208, 0.3);
  flex-shrink: 0;

  h2 {
    color: #40e0d0;
    margin: 0;
    font-size: 1.5rem;
    text-align: center;
    flex: 1;
  }

  .back-button {
    background: rgba(64, 224, 208, 0.2);
    border: 1px solid #40e0d0;
    color: #40e0d0;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(64, 224, 208, 0.3);
      transform: translateY(-2px);
    }
  }

  .game-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 0.9rem;
    color: #a0a0a0;

    span {
      margin-bottom: 0.25rem;
    }
  }
}

.chat-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 1rem 2rem;
}

.messages-list {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding-right: 0.5rem;

  /* Custom scrollbar */
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(0, 20, 40, 0.3);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(64, 224, 208, 0.5);
    border-radius: 4px;

    &:hover {
      background: rgba(64, 224, 208, 0.7);
    }
  }
}

.welcome-message {
  text-align: center;
  color: #40e0d0;
  padding: 2rem;
  background: rgba(64, 224, 208, 0.1);
  border-radius: 12px;
  border: 1px solid rgba(64, 224, 208, 0.3);

  p {
    margin: 0.5rem 0;
    line-height: 1.6;
  }
}

.message {
  display: flex;
  margin-bottom: 1rem;

  &.user-message {
    justify-content: flex-end;

    .message-content {
      background: linear-gradient(135deg, #40e0d0, #20b2aa);
      color: #001428;
      border-radius: 18px 18px 4px 18px;
      max-width: 70%;
    }
  }

  &.ai-message {
    justify-content: flex-start;

    .message-content {
      background: rgba(64, 224, 208, 0.15);
      color: #e0e0e0;
      border: 1px solid rgba(64, 224, 208, 0.3);
      border-radius: 18px 18px 18px 4px;
      max-width: 70%;
    }
  }
}

.message-content {
  padding: 0.75rem 1rem;
  display: flex;
  flex-direction: column;
  word-wrap: break-word;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.message-text {
  line-height: 1.5;
  margin-bottom: 0.25rem;

  &.typing {
    position: relative;
    
    &::after {
      content: '...';
      animation: typing 1.5s infinite;
    }
  }
}

.message-time {
  font-size: 0.75rem;
  opacity: 0.7;
  align-self: flex-end;
}

.chat-input-container {
  padding: 1rem 2rem;
  background: rgba(0, 20, 40, 0.9);
  border-top: 1px solid rgba(64, 224, 208, 0.3);
  flex-shrink: 0;
}

.input-wrapper {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.chat-input {
  flex: 1;
  padding: 0.75rem 1rem;
  background: rgba(64, 224, 208, 0.1);
  border: 1px solid rgba(64, 224, 208, 0.3);
  border-radius: 25px;
  color: #e0e0e0;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;

  &:focus {
    border-color: #40e0d0;
    background: rgba(64, 224, 208, 0.15);
    box-shadow: 0 0 0 2px rgba(64, 224, 208, 0.2);
  }

  &::placeholder {
    color: #a0a0a0;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.send-button {
  padding: 0.75rem 1.5rem;
  background: linear-gradient(135deg, #40e0d0, #20b2aa);
  border: none;
  border-radius: 25px;
  color: #001428;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;

  &:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(64, 224, 208, 0.4);
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

@keyframes typing {
  0%, 20% { opacity: 0; }
  50% { opacity: 1; }
  100% { opacity: 0; }
}

/* Responsive design */
@media (max-width: 768px) {
  .play-header {
    padding: 0.75rem 1rem;
    flex-direction: column;
    gap: 0.5rem;

    .back-button {
      align-self: flex-start;
    }

    .game-info {
      align-self: center;
      text-align: center;
    }
  }

  .chat-container {
    padding: 0.75rem 1rem;
  }

  .chat-input-container {
    padding: 0.75rem 1rem;
  }

  .message {
    &.user-message .message-content,
    &.ai-message .message-content {
      max-width: 85%;
    }
  }

  .input-wrapper {
    gap: 0.5rem;
  }

  .send-button {
    min-width: 70px;
    padding: 0.75rem 1rem;
  }
}
